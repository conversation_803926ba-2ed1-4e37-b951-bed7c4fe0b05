"use client";
import { cn } from "@ui/lib";
import { motion, useInView } from "framer-motion";
import React, { useRef } from "react";

export const PointerHighlight = ({
  children,
  rectangleClassName,
  pointerClassName,
  containerClassName,
}: {
  children: React.ReactNode;
  rectangleClassName?: string;
  pointerClassName?: string;
  containerClassName?: string;
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const isInView = useInView(ref, { once: true });

  return (
    <div ref={ref} className={cn("relative inline-block", containerClassName)}>
      {isInView && (
        <>
          {/* Animated Rectangle Border */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className={cn(
              "absolute inset-0 rounded-lg border-2 border-blue-500 bg-blue-50 dark:bg-blue-950/20 dark:border-blue-400",
              rectangleClassName
            )}
          />
          
          {/* Pointer Icon */}
          <motion.div
            initial={{ opacity: 0, x: -20, y: -20 }}
            animate={{ opacity: 1, x: 0, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3, ease: "easeOut" }}
            className={cn(
              "absolute -top-8 -left-8 text-blue-500 dark:text-blue-400",
              pointerClassName
            )}
          >
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
            >
              <path
                d="M3 3L21 12L12 21L9 12L3 3Z"
                fill="currentColor"
                stroke="currentColor"
                strokeWidth="1"
                strokeLinejoin="round"
              />
            </svg>
          </motion.div>
        </>
      )}
      
      {/* Content */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={isInView ? { opacity: 1 } : { opacity: 0 }}
        transition={{ duration: 0.4, delay: 0.1 }}
        className="relative z-10 px-2 py-1"
      >
        {children}
      </motion.div>
    </div>
  );
};
