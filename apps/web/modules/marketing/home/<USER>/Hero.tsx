import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { ArrowRightIcon, SparklesIcon, ZapIcon, XIcon } from "lucide-react";
import { MultiStepLoader } from "./aceternity/multi-step-loader";
import { useState } from "react";

const loadingStates = [
  {
    text: "Analyzing your Google Business profile...",
  },
  {
    text: "Extracting business information...",
  },
  {
    text: "Gathering photos and reviews...",
  },
  {
    text: "Designing your website layout...",
  },
  {
    text: "Optimizing for mobile devices...",
  },
  {
    text: "Setting up SEO optimization...",
  },
  {
    text: "Finalizing your website...",
  },
  {
    text: "Your stunning website is ready!",
  },
];

export function Hero() {
	const [loading, setLoading] = useState(false);

	const handleGenerateWebsite = () => {
		setLoading(true);
		// Simulate website generation process
		setTimeout(() => {
			setLoading(false);
			// Here you would typically redirect to the generated website or dashboard
		}, 16000); // 8 steps * 2 seconds each
	};
	return (
		<div className="relative min-h-[80vh] sm:min-h-screen flex items-center justify-center overflow-hidden py-12 sm:py-0">
			{/* Gradient Background */}
			<div className="absolute inset-0 bg-gradient-to-br from-background via-card/50 to-muted/30" />

			{/* Subtle Grid Pattern - Optimized for mobile */}
			<div className="absolute inset-0 bg-[linear-gradient(to_right,#8b735510_1px,transparent_1px),linear-gradient(to_bottom,#8b735510_1px,transparent_1px)] bg-[size:2rem_2rem] sm:bg-[size:4rem_4rem] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_110%)]" />

			{/* Floating Elements - Reduced on mobile for performance */}
			<div className="hidden sm:block absolute top-1/4 left-1/4 w-2 h-2 bg-primary/20 rounded-full animate-pulse" />
			<div className="hidden sm:block absolute top-1/3 right-1/4 w-1 h-1 bg-accent/30 rounded-full animate-pulse delay-1000" />
			<div className="hidden sm:block absolute bottom-1/3 left-1/3 w-1.5 h-1.5 bg-secondary/20 rounded-full animate-pulse delay-500" />

			{/* Main Content */}
			<div className="container relative z-20 text-center px-4 sm:px-6 lg:px-8">
				{/* Badge */}
				<div className="inline-flex items-center gap-1.5 px-3 py-1.5 sm:px-4 sm:py-2 rounded-full bg-primary/5 border border-primary/10 text-primary text-xs sm:text-sm font-medium mb-3 sm:mb-6 backdrop-blur-sm">
					<SparklesIcon className="size-3 sm:size-4" />
					<span>AI-Powered Website Generation</span>
				</div>

				{/* Main Heading - Enhanced mobile typography */}
				<h1 className="mx-auto max-w-5xl text-balance font-bold text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl leading-[1.1] sm:leading-tight tracking-tight">
					Transform Your{" "}
					<span className="relative">
						<span className="bg-gradient-to-r from-primary via-accent to-primary bg-clip-text text-transparent">
							Google Business
						</span>
						<div className="absolute -inset-1 bg-gradient-to-r from-primary/20 via-accent/20 to-primary/20 blur-lg -z-10 opacity-50" />
					</span>
					{" "}Into a Stunning Website
				</h1>

				{/* Subtitle - Better mobile spacing */}
				<p className="mx-auto mt-3 sm:mt-6 max-w-2xl text-balance text-foreground/70 text-base sm:text-lg md:text-xl leading-relaxed px-2 sm:px-0">
					Paste your Google Maps link and get a professional website in seconds.
				</p>

				{/* CTA Section */}
				<div className="mt-6 sm:mt-12 max-w-2xl mx-auto">
					<div className="relative group">
						{/* Glow effect */}
						<div className="absolute -inset-0.5 bg-gradient-to-r from-primary/20 via-accent/20 to-primary/20 rounded-2xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

						<div className="relative flex flex-col sm:flex-row items-center gap-3 p-2 sm:p-3 rounded-xl sm:rounded-2xl bg-card/80 backdrop-blur-md border border-border/60 shadow-xl">
							{/* Enhanced Input */}
							<div className="relative flex-1 w-full">
								<Input
									type="url"
									placeholder="https://maps.google.com/your-business-link"
									className="h-11 sm:h-14 px-3 sm:px-4 text-sm sm:text-base bg-background/50 border border-border/30 rounded-lg sm:rounded-xl focus-visible:border-primary/50 focus-visible:ring-2 focus-visible:ring-primary/20 focus-visible:bg-background/80 placeholder:text-foreground/40 transition-all duration-300 hover:border-border/60"
								/>
								{/* Input shine effect */}
								<div className="absolute inset-0 rounded-lg sm:rounded-xl bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
							</div>

							{/* Enhanced Button */}
							<div className="relative w-full sm:w-auto">
								{/* Button glow */}
								<div className="absolute -inset-1 bg-gradient-to-r from-primary via-accent to-primary rounded-lg sm:rounded-xl blur-sm opacity-0 group-hover:opacity-60 transition-opacity duration-300" />

								<Button
									size="lg"
									variant="primary"
									onClick={handleGenerateWebsite}
									disabled={loading}
									className="relative h-11 sm:h-14 w-full sm:w-auto px-6 sm:px-8 rounded-lg sm:rounded-xl font-semibold text-sm sm:text-base bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary shadow-lg shadow-primary/25 hover:shadow-xl hover:shadow-primary/40 border-0 transition-all duration-300 group/btn overflow-hidden disabled:opacity-50 disabled:cursor-not-allowed"
								>
									{/* Button shine effect */}
									<div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover/btn:translate-x-full transition-transform duration-700 ease-out" />

									<div className="relative flex items-center justify-center">
										<ZapIcon className="mr-2 size-4 sm:size-5 group-hover/btn:scale-110 group-hover/btn:rotate-12 transition-all duration-300" />
										<span className="font-semibold">Generate Website</span>
										<ArrowRightIcon className="ml-2 size-3 sm:size-4 group-hover/btn:translate-x-1 transition-transform duration-300" />
									</div>
								</Button>
							</div>
						</div>
					</div>

					{/* Trust Indicators */}
					<div className="flex flex-row items-center justify-center gap-2 sm:gap-6 mt-6 sm:mt-8 text-xs sm:text-sm text-foreground/60">
						<div className="flex items-center gap-1 sm:gap-2">
							<div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-success rounded-full" />
							<span className="whitespace-nowrap">No credit card</span>
						</div>
						<div className="flex items-center gap-1 sm:gap-2">
							<div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-success rounded-full" />
							<span className="whitespace-nowrap">30-sec setup</span>
						</div>
						<div className="flex items-center gap-1 sm:gap-2">
							<div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-success rounded-full" />
							<span className="whitespace-nowrap">Mobile ready</span>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
